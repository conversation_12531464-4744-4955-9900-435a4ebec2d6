import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { hasPermission } from "@/lib/permissions"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const updateTaskSchema = z.object({
  status: z.enum(["TO_DO", "IN_PROGRESS", "PENDING", "REFERENCE_PENDING", "COMPLETED"]).optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  priority: z.enum(["URGENT", "HIGH", "MEDIUM", "LOW"]).optional(),
  assigneeId: z.string().optional(),
  dueDate: z.string().optional(),
})

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const body = await request.json()
    const updates = updateTaskSchema.parse(body)

    // Get the task to check permissions
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        project: {
          include: {
            team: {
              include: {
                members: true,
              },
            },
          },
        },
      },
    })

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    // Check if user has access to this task
    const isTeamMember = task.project.team.members.some((member) => member.userId === session.user.id)

    if (!isTeamMember) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Check permissions for different update types
    if (updates.assigneeId && !hasPermission(userRole, "ASSIGN_TASK")) {
      return NextResponse.json({ error: "Insufficient permissions to assign tasks" }, { status: 403 })
    }

    // Crew members can only update status of their assigned tasks
    if (userRole === UserRole.CREW_MEMBER) {
      if (task.assigneeId !== session.user.id) {
        return NextResponse.json({ error: "You can only update your assigned tasks" }, { status: 403 })
      }

      // Only allow status updates for crew members
      const allowedUpdates = { status: updates.status }
      Object.keys(updates).forEach((key) => {
        if (key !== "status") {
          delete (updates as any)[key]
        }
      })
    }

    // Log the activity
    const oldTask = { ...task }

    const updatedTask = await prisma.task.update({
      where: { id: params.id },
      data: updates,
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        reporter: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    // Create activity log
    if (updates.status && updates.status !== oldTask.status) {
      await prisma.taskActivity.create({
        data: {
          taskId: params.id,
          userId: session.user.id,
          action: "status_changed",
          oldValue: oldTask.status,
          newValue: updates.status,
          description: `Status changed from ${oldTask.status} to ${updates.status}`,
        },
      })
    }

    return NextResponse.json({ task: updatedTask })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 })
    }

    console.error("Update task error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
