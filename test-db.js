// Simple script to test database connection
const { Pool } = require("pg")
require("dotenv").config()

async function testConnection() {
  console.log("Testing database connection...")
  console.log("Connection string:", process.env.DATABASE_URL)

  const connectionString = process.env.DATABASE_URL

  if (!connectionString) {
    console.error("No DATABASE_URL found in environment variables")
    return
  }

  const pool = new Pool({
    connectionString,
    ssl: { rejectUnauthorized: false },
  })

  try {
    console.log("Connecting to database...")
    const client = await pool.connect()
    console.log("✅ Connected successfully!")

    const result = await client.query("SELECT NOW()")
    console.log("Database time:", result.rows[0].now)

    client.release()
  } catch (err) {
    console.error("❌ Connection failed:", err.message)
  } finally {
    await pool.end()
  }
}

testConnection()
