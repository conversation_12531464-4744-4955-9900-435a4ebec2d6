import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const joinTeamSchema = z.object({
  teamCode: z.string().length(6, "Team code must be 6 characters"),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { teamCode } = joinTeamSchema.parse(body)

    // Find team by code
    const team = await prisma.team.findUnique({
      where: { teamCode: teamCode.toUpperCase() },
    })

    if (!team) {
      return NextResponse.json({ error: "Invalid team code" }, { status: 404 })
    }

    // Check if user is already a member
    const existingMembership = await prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          userId: session.user.id,
          teamId: team.id,
        },
      },
    })

    if (existingMembership) {
      return NextResponse.json({ error: "You are already a member of this team" }, { status: 400 })
    }

    // Add user to team
    const membership = await prisma.teamMember.create({
      data: {
        userId: session.user.id,
        teamId: team.id,
        role: "CREW_MEMBER", // Default role for joining users
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    })

    return NextResponse.json({
      message: "Successfully joined team",
      membership,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 })
    }

    console.error("Join team error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
