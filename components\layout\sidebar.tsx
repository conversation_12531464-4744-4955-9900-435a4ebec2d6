"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  LayoutDashboard,
  CheckSquare,
  Users,
  Settings,
  BarChart3,
  Shield,
  Slack,
  ChevronLeft,
  ChevronRight,
  Building,
  FileText,
  Clock,
} from "lucide-react"
import { hasPermission } from "@/lib/permissions"
import type { UserRole } from "@prisma/client"

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false)
  const { data: session } = useSession()
  const pathname = usePathname()

  if (!session?.user) return null

  const userRole = session.user.role as UserRole

  const navigationItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      permission: "MEMBER_DASHBOARD" as const,
    },
    {
      title: "Tasks",
      href: "/tasks",
      icon: CheckSquare,
      permission: "UPDATE_TASK_STATUS" as const,
    },
    {
      title: "Projects",
      href: "/projects",
      icon: Building,
      permission: "MEMBER_DASHBOARD" as const,
    },
    {
      title: "QA Review",
      href: "/qa",
      icon: Shield,
      permission: "ACCESS_QA" as const,
    },
    {
      title: "Team",
      href: "/team",
      icon: Users,
      permission: "MANAGE_TEAM" as const,
    },
    {
      title: "Analytics",
      href: "/analytics",
      icon: BarChart3,
      permission: "VIEW_ANALYTICS" as const,
    },
    {
      title: "Reports",
      href: "/reports",
      icon: FileText,
      permission: "EXPORT_REPORTS" as const,
    },
    {
      title: "Audit Logs",
      href: "/audit",
      icon: Clock,
      permission: "VIEW_AUDIT_LOGS" as const,
    },
    {
      title: "Slack Integration",
      href: "/slack",
      icon: Slack,
      permission: "MANAGE_SLACK" as const,
    },
    {
      title: "Settings",
      href: "/settings",
      icon: Settings,
      permission: "MEMBER_DASHBOARD" as const,
    },
  ]

  const filteredItems = navigationItems.filter((item) => hasPermission(userRole, item.permission))

  return (
    <div
      className={cn(
        "flex flex-col border-r bg-background transition-all duration-300",
        collapsed ? "w-16" : "w-64",
        className,
      )}
    >
      <div className="flex h-16 items-center justify-between px-4 border-b">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">Y</span>
            </div>
            <span className="font-semibold text-lg">YARK</span>
          </div>
        )}
        <Button variant="ghost" size="icon" onClick={() => setCollapsed(!collapsed)} className="h-8 w-8">
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-2">
          {filteredItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon

            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn("w-full justify-start gap-3 h-10", collapsed && "justify-center px-2")}
                  title={collapsed ? item.title : undefined}
                >
                  <Icon className="h-4 w-4 shrink-0" />
                  {!collapsed && <span>{item.title}</span>}
                </Button>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>

      <div className="border-t p-4">
        {!collapsed && (
          <div className="space-y-2">
            <div className="text-sm font-medium">{session.user.name}</div>
            <div className="text-xs text-muted-foreground">{session.user.email}</div>
            <div className="text-xs text-muted-foreground capitalize">{userRole.toLowerCase().replace("_", " ")}</div>
          </div>
        )}
      </div>
    </div>
  )
}
