import { PrismaClient, UserRole, TaskStatus, TaskPriority } from "@prisma/client"
import bcrypt from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  console.log("🌱 Starting database seed...")

  // Create demo users
  const adminPassword = await bcrypt.hash("admin123", 12)
  const leaderPassword = await bcrypt.hash("leader123", 12)
  const memberPassword = await bcrypt.hash("member123", 12)

  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Admin User",
      password: adminPassword,
      role: UserRole.ADMIN,
    },
  })

  const leader = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Team Leader",
      password: leaderPassword,
      role: UserRole.CREW_LEADER,
    },
  })

  const member = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Team Member",
      password: memberPassword,
      role: UserRole.CREW_MEMBER,
    },
  })

  // Create demo team
  const team = await prisma.team.upsert({
    where: { teamCode: "DEMO01" },
    update: {},
    create: {
      name: "Demo Team",
      description: "A demonstration team for YARK platform",
      teamCode: "DEMO01",
    },
  })

  // Add users to team
  await prisma.teamMember.upsert({
    where: {
      userId_teamId: {
        userId: admin.id,
        teamId: team.id,
      },
    },
    update: {},
    create: {
      userId: admin.id,
      teamId: team.id,
      role: UserRole.ADMIN,
    },
  })

  await prisma.teamMember.upsert({
    where: {
      userId_teamId: {
        userId: leader.id,
        teamId: team.id,
      },
    },
    update: {},
    create: {
      userId: leader.id,
      teamId: team.id,
      role: UserRole.CREW_LEADER,
    },
  })

  await prisma.teamMember.upsert({
    where: {
      userId_teamId: {
        userId: member.id,
        teamId: team.id,
      },
    },
    update: {},
    create: {
      userId: member.id,
      teamId: team.id,
      role: UserRole.CREW_MEMBER,
    },
  })

  // Create demo project
  const project = await prisma.project.upsert({
    where: { id: "demo-project-1" },
    update: {},
    create: {
      id: "demo-project-1",
      name: "Website Redesign",
      description: "Complete redesign of the company website",
      teamId: team.id,
    },
  })

  // Create demo tasks
  const tasks = [
    {
      title: "Design Homepage Mockup",
      description: "Create wireframes and mockups for the new homepage design",
      status: TaskStatus.COMPLETED,
      priority: TaskPriority.HIGH,
      reporterId: admin.id,
      assigneeId: leader.id,
      projectId: project.id,
    },
    {
      title: "Implement Navigation Component",
      description: "Build responsive navigation component with mobile menu",
      status: TaskStatus.IN_PROGRESS,
      priority: TaskPriority.MEDIUM,
      reporterId: leader.id,
      assigneeId: member.id,
      projectId: project.id,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    },
    {
      title: "Content Migration",
      description: "Migrate existing content to new CMS structure",
      status: TaskStatus.TO_DO,
      priority: TaskPriority.LOW,
      reporterId: admin.id,
      assigneeId: member.id,
      projectId: project.id,
    },
    {
      title: "Performance Optimization",
      description: "Optimize images and implement lazy loading",
      status: TaskStatus.PENDING,
      priority: TaskPriority.URGENT,
      reporterId: leader.id,
      assigneeId: member.id,
      projectId: project.id,
      dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago (overdue)
    },
  ]

  for (const taskData of tasks) {
    await prisma.task.create({
      data: taskData,
    })
  }

  console.log("✅ Database seeded successfully!")
  console.log("\n📧 Demo Credentials:")
  console.log("Admin: <EMAIL> / admin123")
  console.log("Leader: <EMAIL> / leader123")
  console.log("Member: <EMAIL> / member123")
  console.log("\n🏢 Demo Team Code: DEMO01")
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
