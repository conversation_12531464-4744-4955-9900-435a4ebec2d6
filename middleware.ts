import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export default withAuth(
  function middleware(req: NextRequest) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Allow access to auth pages when not authenticated
    if (!token && pathname.startsWith("/auth")) {
      return NextResponse.next()
    }

    // Redirect to signin if not authenticated
    if (!token && !pathname.startsWith("/auth")) {
      const signInUrl = new URL("/auth/signin", req.url)
      signInUrl.searchParams.set("callbackUrl", pathname)
      return NextResponse.redirect(signInUrl)
    }

    // Redirect authenticated users away from auth pages
    if (token && pathname.startsWith("/auth")) {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    // Role-based route protection
    const userRole = token?.role

    // Admin-only routes
    const adminRoutes = ["/admin", "/audit", "/reports"]
    if (adminRoutes.some((route) => pathname.startsWith(route)) && userRole !== "ADMIN") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    // Leader and Admin routes
    const leaderRoutes = ["/qa", "/analytics", "/team"]
    if (
      leaderRoutes.some((route) => pathname.startsWith(route)) &&
      !["ADMIN", "CREW_LEADER"].includes(userRole || "")
    ) {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to API routes and auth pages
        if (req.nextUrl.pathname.startsWith("/api") || req.nextUrl.pathname.startsWith("/auth")) {
          return true
        }

        // Require authentication for all other routes
        return !!token
      },
    },
  },
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
}
