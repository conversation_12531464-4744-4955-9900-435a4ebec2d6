"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import { AdminDashboard } from "@/components/dashboard/admin-dashboard"
import { OnboardingModal } from "@/components/onboarding/onboarding-modal"
import { Sidebar } from "@/components/layout/sidebar"
import { UserRole } from "@prisma/client"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [hasTeam, setHasTeam] = useState(false)

  useEffect(() => {
    if (session?.user) {
      checkUserTeamStatus()
    }
  }, [session])

  const checkUserTeamStatus = async () => {
    try {
      const response = await fetch("/api/user/teams")
      const data = await response.json()

      if (data.teams && data.teams.length > 0) {
        setHasTeam(true)
      } else {
        setShowOnboarding(true)
      }
    } catch (error) {
      console.error("Failed to check team status:", error)
      setShowOnboarding(true)
    }
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const userRole = session.user.role as UserRole

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {userRole === UserRole.ADMIN && <AdminDashboard />}
          {userRole === UserRole.CREW_LEADER && <div>Crew Leader Dashboard (Coming Soon)</div>}
          {userRole === UserRole.CREW_MEMBER && <div>Crew Member Dashboard (Coming Soon)</div>}
          {userRole === UserRole.GUEST && <div>Guest Dashboard (Coming Soon)</div>}
        </main>
      </div>

      <OnboardingModal isOpen={showOnboarding} onClose={() => setShowOnboarding(false)} userRole={userRole} />
    </div>
  )
}
