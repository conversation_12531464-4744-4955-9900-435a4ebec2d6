@echo off
echo 🚀 Setting up YARK Platform with SQLite...

echo 📦 Installing dependencies...
call npm install --legacy-peer-deps

echo 🔧 Generating Prisma client...
call npx prisma generate

echo 🗄️ Creating SQLite database...
call npx prisma db push

echo 🌱 Seeding database with demo data...
call npx tsx prisma/seed.ts

echo ✅ Setup complete!
echo.
echo 🎉 You can now start the development server:
echo    npm run dev
echo.
echo 📧 Demo credentials:
echo    Admin: <EMAIL> / admin123
echo    Leader: <EMAIL> / leader123
echo    Member: <EMAIL> / member123
echo.
echo 🏢 Demo team code: DEMO01
pause
