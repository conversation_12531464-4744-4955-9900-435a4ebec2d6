# YARK - Task Management Platform

A comprehensive enterprise-grade task tracking and team collaboration platform with real Slack integration and advanced workflow support.

## Features

### 🔐 Role-Based Access Control (RBAC)
- **Admin**: Full system privileges, user management, analytics, exports
- **Crew Leader**: Team management, task creation, QA workflow access
- **Crew Member**: View and update assigned tasks, add comments
- **Guest**: Read-only access to shared dashboards

### 🚀 Core Functionality
- **User Authentication**: JWT-based auth with 2FA support
- **Team Management**: Create/join teams with unique codes
- **Task Management**: Hierarchical tasks with subtasks, priorities, due dates
- **Quality Assurance**: Built-in QA workflow for task verification
- **Real-time Collaboration**: Live updates and notifications
- **Analytics Dashboard**: Role-based dashboards with insights

### 💬 Slack Integration
- **OAuth 2.0 Setup**: Secure team-based Slack workspace connections
- **Real-time Notifications**: Task assignments, status changes, mentions
- **Interactive Messages**: Action buttons for quick task updates
- **Slash Commands**: `/yark` commands for task management
- **Digest Reports**: Daily/weekly team summaries

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS, Radix UI
- **Backend**: Next.js API Routes, Prisma ORM, PostgreSQL
- **Authentication**: NextAuth.js with JWT and 2FA
- **Real-time**: WebSockets/SSE for live collaboration
- **Slack**: Slack Bolt SDK for integration
- **Deployment**: Docker, Vercel/Railway ready

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Slack app (for integration)

### Installation

1. **Clone the repository**
   \`\`\`bash
   git clone <repository-url>
   cd yark-platform
   \`\`\`

2. **Install dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Set up environment variables**
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your configuration
   \`\`\`

4. **Set up the database**
   \`\`\`bash
   npx prisma generate
   npx prisma db push
   \`\`\`

5. **Run the development server**
   \`\`\`bash
   npm run dev
   \`\`\`

6. **Access the application**
   Open [http://localhost:3000](http://localhost:3000)

## Environment Variables

\`\`\`env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/yark_db"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# JWT Secrets
JWT_SECRET="your-jwt-secret-key"
JWT_REFRESH_SECRET="your-jwt-refresh-secret-key"

# Slack Integration
SLACK_CLIENT_ID="your-slack-client-id"
SLACK_CLIENT_SECRET="your-slack-client-secret"
SLACK_SIGNING_SECRET="your-slack-signing-secret"
\`\`\`

## Database Schema

The application uses PostgreSQL with Prisma ORM. Key entities:

- **Users**: Authentication, roles, 2FA settings
- **Teams**: Multi-tenant team structure with unique codes
- **Projects**: Team-scoped project organization
- **Tasks**: Hierarchical task structure with full workflow
- **QA Records**: Quality assurance tracking
- **Audit Logs**: Complete activity tracking
- **Slack Integration**: Per-team Slack workspace connections

## API Documentation

### Authentication Endpoints
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login with 2FA support

### Team Management
- `POST /api/teams` - Create new team (Admin only)
- `POST /api/teams/join` - Join team with code
- `GET /api/user/teams` - Get user's teams

### Task Management
- `GET /api/tasks` - Get tasks (filtered by role)
- `PATCH /api/tasks/[id]` - Update task status/details
- `POST /api/tasks` - Create new task

### Dashboard
- `GET /api/dashboard/admin` - Admin dashboard stats
- `GET /api/dashboard/leader` - Team leader dashboard
- `GET /api/dashboard/member` - Member dashboard

## Deployment

### Docker Deployment

1. **Build the image**
   \`\`\`bash
   docker build -t yark-platform .
   \`\`\`

2. **Run the container**
   \`\`\`bash
   docker run -p 3000:3000 --env-file .env yark-platform
   \`\`\`

### Vercel Deployment

1. **Connect your repository to Vercel**
2. **Set environment variables in Vercel dashboard**
3. **Deploy automatically on push**

## Slack App Setup

1. **Create a Slack App** at [api.slack.com](https://api.slack.com)
2. **Configure OAuth & Permissions**:
   - Add redirect URL: `https://your-domain.com/api/slack/oauth`
   - Required scopes: `chat:write`, `commands`, `users:read`
3. **Enable Slash Commands**:
   - `/yark tasks` - View assigned tasks
   - `/yark create` - Create new task
   - `/yark update` - Update task status
4. **Set up Event Subscriptions** for real-time updates

## Security Features

- **JWT Authentication** with refresh tokens
- **Two-Factor Authentication** (TOTP)
- **Role-based access control** on all endpoints
- **Input validation** with Zod schemas
- **Rate limiting** on sensitive endpoints
- **Audit logging** for all admin actions
- **CORS protection** and secure headers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

---

**YARK** - Streamlining task management and team collaboration with enterprise-grade security and Slack integration.
