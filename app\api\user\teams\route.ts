import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const teams = await prisma.teamMember.findMany({
      where: { userId: session.user.id },
      include: {
        team: {
          select: {
            id: true,
            name: true,
            description: true,
            teamCode: true,
            createdAt: true,
          },
        },
      },
    })

    return NextResponse.json({
      teams: teams.map((tm) => ({
        ...tm.team,
        role: tm.role,
        joinedAt: tm.joinedAt,
      })),
    })
  } catch (error) {
    console.error("Get user teams error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
