import type { NextAuthOptions } from "next-auth"
import Credentials<PERSON>rovider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"
import jwt from "jsonwebtoken"
import type { UserRole } from "@prisma/client"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        totpCode: { label: "2FA Code", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: { teamMemberships: { include: { team: true } } },
        })

        if (!user || !user.isActive) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
        if (!isPasswordValid) {
          return null
        }

        // Check 2FA if enabled
        if (user.twoFactorEnabled && user.twoFactorSecret) {
          if (!credentials.totpCode) {
            throw new Error("2FA_REQUIRED")
          }

          try {
            const speakeasy = require("speakeasy")
            const verified = speakeasy.totp.verify({
              secret: user.twoFactorSecret,
              encoding: "base32",
              token: credentials.totpCode,
              window: 2,
            })

            if (!verified) {
              throw new Error("INVALID_2FA")
            }
          } catch (error) {
            // If speakeasy is not available, skip 2FA for now
            console.warn("2FA verification skipped - speakeasy not available")
          }
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          teams: user.teamMemberships.map((tm) => ({
            id: tm.team.id,
            name: tm.team.name,
            role: tm.role,
          })),
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.teams = user.teams
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.teams = token.teams as any[]
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
}

export function generateAccessToken(userId: string, role: UserRole) {
  return jwt.sign({ userId, role }, process.env.JWT_SECRET!, { expiresIn: "15m" })
}

export function generateRefreshToken() {
  return jwt.sign({ type: "refresh" }, process.env.JWT_REFRESH_SECRET!, { expiresIn: "7d" })
}
