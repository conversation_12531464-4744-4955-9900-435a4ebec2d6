"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Users, UserPlus, Building } from "lucide-react"

interface OnboardingModalProps {
  isOpen: boolean
  onClose: () => void
  userRole: string
}

export function OnboardingModal({ isOpen, onClose, userRole }: OnboardingModalProps) {
  const [step, setStep] = useState<"choice" | "create" | "join">("choice")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Create team form
  const [teamData, setTeamData] = useState({
    name: "",
    description: "",
  })

  // Join team form
  const [teamCode, setTeamCode] = useState("")

  const handleCreateTeam = async () => {
    if (!teamData.name.trim()) {
      setError("Team name is required")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/teams", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(teamData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to create team")
      }

      setSuccess(`Team created successfully! Team code: ${data.teamCode}`)
      setTimeout(() => {
        onClose()
        window.location.reload()
      }, 2000)
    } catch (error: any) {
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleJoinTeam = async () => {
    if (!teamCode.trim()) {
      setError("Team code is required")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/teams/join", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ teamCode }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to join team")
      }

      setSuccess("Successfully joined team!")
      setTimeout(() => {
        onClose()
        window.location.reload()
      }, 2000)
    } catch (error: any) {
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const renderChoiceStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Welcome to YARK!</h3>
        <p className="text-gray-600">To get started, you need to be part of a team.</p>
      </div>

      <div className="grid gap-4">
        {userRole === "ADMIN" && (
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setStep("create")}>
            <CardHeader className="text-center">
              <Building className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <CardTitle className="text-lg">Create New Team</CardTitle>
              <CardDescription>Start a new team and invite members</CardDescription>
            </CardHeader>
          </Card>
        )}

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setStep("join")}>
          <CardHeader className="text-center">
            <UserPlus className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <CardTitle className="text-lg">Join Existing Team</CardTitle>
            <CardDescription>Use a team code to join an existing team</CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  )

  const renderCreateStep = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold">Create Your Team</h3>
        <p className="text-gray-600">Set up a new team for your organization</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="teamName">Team Name *</Label>
          <Input
            id="teamName"
            value={teamData.name}
            onChange={(e) => setTeamData((prev) => ({ ...prev, name: e.target.value }))}
            placeholder="Enter team name"
            disabled={isLoading}
          />
        </div>

        <div>
          <Label htmlFor="teamDescription">Description</Label>
          <Textarea
            id="teamDescription"
            value={teamData.description}
            onChange={(e) => setTeamData((prev) => ({ ...prev, description: e.target.value }))}
            placeholder="Describe your team (optional)"
            disabled={isLoading}
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setStep("choice")} disabled={isLoading}>
            Back
          </Button>
          <Button onClick={handleCreateTeam} disabled={isLoading} className="flex-1">
            {isLoading ? "Creating..." : "Create Team"}
          </Button>
        </div>
      </div>
    </div>
  )

  const renderJoinStep = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold">Join a Team</h3>
        <p className="text-gray-600">Enter the 6-digit team code to join</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="teamCode">Team Code *</Label>
          <Input
            id="teamCode"
            value={teamCode}
            onChange={(e) => setTeamCode(e.target.value.toUpperCase())}
            placeholder="Enter 6-digit team code"
            maxLength={6}
            disabled={isLoading}
            className="text-center text-lg tracking-widest"
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setStep("choice")} disabled={isLoading}>
            Back
          </Button>
          <Button onClick={handleJoinTeam} disabled={isLoading} className="flex-1">
            {isLoading ? "Joining..." : "Join Team"}
          </Button>
        </div>
      </div>
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md" hideClose>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Setup
          </DialogTitle>
          <DialogDescription>Complete your setup to start using YARK</DialogDescription>
        </DialogHeader>

        {step === "choice" && renderChoiceStep()}
        {step === "create" && renderCreateStep()}
        {step === "join" && renderJoinStep()}
      </DialogContent>
    </Dialog>
  )
}
