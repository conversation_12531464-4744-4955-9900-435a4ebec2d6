// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

enum UserRole {
  ADMIN
  CREW_LEADER
  CREW_MEMBER
  GUEST
}

enum TaskStatus {
  TO_DO
  IN_PROGRESS
  PENDING
  REFERENCE_PENDING
  COMPLETED
}

enum TaskPriority {
  URGENT
  HIGH
  MEDIUM
  LOW
}

enum QAStatus {
  START
  PENDING
  CHECKED
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  name              String
  password          String
  role              UserRole  @default(ADMIN)
  isActive          Boolean   @default(true)
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?
  slackUserId       String?
  avatar            String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  teamMemberships   TeamMember[]
  createdTasks      Task[]       @relation("TaskReporter")
  assignedTasks     Task[]       @relation("TaskAssignee")
  comments          Comment[]
  qaRecords         QARecord[]
  auditLogs         AuditLog[]
  refreshTokens     RefreshToken[]

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model Team {
  id          String   @id @default(cuid())
  name        String
  description String?
  teamCode    String   @unique
  slackTeamId String?
  slackToken  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  members  TeamMember[]
  projects Project[]
  slackIntegration SlackIntegration?

  @@map("teams")
}

model TeamMember {
  id     String   @id @default(cuid())
  userId String
  teamId String
  role   UserRole @default(CREW_MEMBER)
  joinedAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@unique([userId, teamId])
  @@map("team_members")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  teamId      String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  team  Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)
  tasks Task[]

  @@map("projects")
}

model Task {
  id           String       @id @default(cuid())
  title        String
  description  String?
  status       TaskStatus   @default(TO_DO)
  priority     TaskPriority @default(MEDIUM)
  dueDate      DateTime?
  estimatedTime Int?        // in minutes
  projectId    String
  reporterId   String
  assigneeId   String?
  parentTaskId String?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // Relations
  project     Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
  reporter    User         @relation("TaskReporter", fields: [reporterId], references: [id])
  assignee    User?        @relation("TaskAssignee", fields: [assigneeId], references: [id])
  parentTask  Task?        @relation("TaskSubtasks", fields: [parentTaskId], references: [id])
  subtasks    Task[]       @relation("TaskSubtasks")
  comments    Comment[]
  attachments Attachment[]
  qaRecords   QARecord[]
  activities  TaskActivity[]

  @@map("tasks")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  taskId    String
  authorId  String
  mentions  String[] // Array of user IDs mentioned
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  task   Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  author User @relation(fields: [authorId], references: [id])

  @@map("comments")
}

model Attachment {
  id       String @id @default(cuid())
  filename String
  fileUrl  String
  fileSize Int
  mimeType String
  taskId   String
  uploadedAt DateTime @default(now())

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("attachments")
}

model QARecord {
  id                String    @id @default(cuid())
  taskId            String
  reviewerId        String
  status            QAStatus  @default(START)
  timeSpent         Int?      // in minutes
  comments          String?
  verificationDuration Int?   // in minutes
  reviewedAt        DateTime?
  createdAt         DateTime  @default(now())

  task     Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  reviewer User @relation(fields: [reviewerId], references: [id])

  @@map("qa_records")
}

model TaskActivity {
  id          String   @id @default(cuid())
  taskId      String
  userId      String
  action      String   // e.g., "status_changed", "assigned", "commented"
  oldValue    String?
  newValue    String?
  description String
  createdAt   DateTime @default(now())

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("task_activities")
}

model AuditLog {
  id          String   @id @default(cuid())
  userId      String
  action      String
  resource    String   // e.g., "user", "team", "task"
  resourceId  String
  details     Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model SlackIntegration {
  id              String   @id @default(cuid())
  teamId          String   @unique
  slackTeamId     String
  accessToken     String
  botToken        String
  webhookUrl      String?
  notificationChannel String?
  digestFrequency String   @default("daily") // daily, weekly
  digestTime      String   @default("09:00")
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@map("slack_integrations")
}
