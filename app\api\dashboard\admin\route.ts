import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { hasPermission } from "@/lib/permissions"
import type { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userRole = session.user.role as UserRole

    if (!hasPermission(userRole, "ADMIN_DASHBOARD")) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    // Fetch dashboard statistics
    const [totalUsers, totalTeams, totalTasks, completedTasks, pendingQA, overdueTask, activeProjects] =
      await Promise.all([
        prisma.user.count({ where: { isActive: true } }),
        prisma.team.count(),
        prisma.task.count(),
        prisma.task.count({ where: { status: "COMPLETED" } }),
        prisma.qARecord.count({ where: { status: "PENDING" } }),
        prisma.task.count({
          where: {
            dueDate: { lt: new Date() },
            status: { not: "COMPLETED" },
          },
        }),
        prisma.project.count({ where: { isActive: true } }),
      ])

    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

    return NextResponse.json({
      totalUsers,
      totalTeams,
      totalTasks,
      completedTasks,
      pendingQA,
      overdueTask,
      activeProjects,
      completionRate,
    })
  } catch (error) {
    console.error("Admin dashboard error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
