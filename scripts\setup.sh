#!/bin/bash

echo "🚀 Setting up YARK Platform..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if PostgreSQL is running
if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL not found. Make sure you have a PostgreSQL database running."
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Push database schema
echo "🗄️  Setting up database schema..."
npx prisma db push

# Seed database with demo data
echo "🌱 Seeding database with demo data..."
npm run db:seed

echo "✅ Setup complete!"
echo ""
echo "🎉 You can now start the development server:"
echo "   npm run dev"
echo ""
echo "📧 Demo credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Leader: <EMAIL> / leader123"
echo "   Member: <EMAIL> / member123"
echo ""
echo "🏢 Demo team code: DEMO01"
