"use client"

import { useSession } from "next-auth/react"
import { Sidebar } from "@/components/layout/sidebar"
import { TaskKanban } from "@/components/tasks/task-kanban"

export default function TasksPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          <TaskKanban />
        </main>
      </div>
    </div>
  )
}
