import { UserRole } from "@prisma/client"

export const PERMISSIONS = {
  // User Management
  CREATE_USER: [UserRole.ADMIN],
  DELETE_USER: [UserRole.ADMIN],
  MANAGE_USERS: [UserRole.ADMIN],

  // Team Management
  CREATE_TEAM: [UserRole.ADMIN],
  DELETE_TEAM: [UserRole.ADMIN],
  MANAGE_TEAM: [UserRole.ADMIN, UserRole.CREW_LEADER],

  // Task Management
  CREATE_TASK: [UserRole.ADMIN, UserRole.CREW_LEADER],
  DELETE_TASK: [UserRole.ADMIN, UserRole.CREW_LEADER],
  ASSIGN_TASK: [UserRole.ADMIN, UserRole.CREW_LEADER],
  UPDATE_TASK_STATUS: [UserRole.ADMIN, UserRole.CREW_LEADER, UserRole.CREW_MEMBER],

  // QA Management
  ACCESS_QA: [UserRole.ADMIN, UserRole.CREW_LEADER],
  PERFORM_QA: [UserRole.ADMIN, UserRole.CREW_LEADER],

  // Analytics & Reports
  VIEW_ANALYTICS: [UserRole.ADMIN, UserRole.CREW_LEADER],
  EXPORT_REPORTS: [UserRole.ADMIN],
  VIEW_AUDIT_LOGS: [UserRole.ADMIN],

  // Slack Integration
  MANAGE_SLACK: [UserRole.ADMIN],

  // Dashboard Access
  ADMIN_DASHBOARD: [UserRole.ADMIN],
  LEADER_DASHBOARD: [UserRole.ADMIN, UserRole.CREW_LEADER],
  MEMBER_DASHBOARD: [UserRole.ADMIN, UserRole.CREW_LEADER, UserRole.CREW_MEMBER],
  GUEST_DASHBOARD: [UserRole.GUEST],
} as const

export function hasPermission(userRole: UserRole, permission: keyof typeof PERMISSIONS): boolean {
  return PERMISSIONS[permission].includes(userRole)
}

export function requirePermission(userRole: UserRole, permission: keyof typeof PERMISSIONS) {
  if (!hasPermission(userRole, permission)) {
    throw new Error(`Insufficient permissions. Required: ${permission}`)
  }
}
