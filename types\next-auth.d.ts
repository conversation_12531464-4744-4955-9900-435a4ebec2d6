import type { UserR<PERSON> } from "@prisma/client"
import "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      avatar?: string
      teams?: Array<{
        id: string
        name: string
        role: UserRole
      }>
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    avatar?: string
    teams?: Array<{
      id: string
      name: string
      role: UserRole
    }>
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: UserRole
    teams?: Array<{
      id: string
      name: string
      role: UserRole
    }>
  }
}
