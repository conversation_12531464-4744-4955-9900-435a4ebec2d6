# Database Setup Guide

## Option 1: Local PostgreSQL

1. **Install PostgreSQL** (if not already installed):
   - Windows: Download from https://www.postgresql.org/download/windows/
   - macOS: `brew install postgresql`
   - Linux: `sudo apt-get install postgresql`

2. **Create Database**:
   \`\`\`sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create database
   CREATE DATABASE yark_db;
   
   -- Create user (optional)
   CREATE USER yark_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE yark_db TO yark_user;
   \`\`\`

3. **Update .env.local**:
   \`\`\`env
   DATABASE_URL="postgresql://postgres:your_password@localhost:5432/yark_db"
   # OR with custom user:
   DATABASE_URL="postgresql://yark_user:your_password@localhost:5432/yark_db"
   \`\`\`

## Option 2: Docker PostgreSQL

1. **Run PostgreSQL in Docker**:
   \`\`\`bash
   docker run --name yark-postgres \
     -e POSTGRES_DB=yark_db \
     -e POSTGRES_USER=yark_user \
     -e POSTGRES_PASSWORD=yark_password \
     -p 5432:5432 \
     -d postgres:15
   \`\`\`

2. **Update .env.local**:
   \`\`\`env
   DATABASE_URL="postgresql://yark_user:yark_password@localhost:5432/yark_db"
   \`\`\`

## Option 3: Cloud Database (Recommended for Production)

### Supabase (Free Tier Available)
1. Go to https://supabase.com
2. Create new project
3. Copy the connection string from Settings > Database
4. Update .env.local with the provided URL

### Neon (Free Tier Available)
1. Go to https://neon.tech
2. Create new project
3. Copy the connection string
4. Update .env.local with the provided URL

## Verify Connection
\`\`\`bash
# Test the connection
npx prisma db push
